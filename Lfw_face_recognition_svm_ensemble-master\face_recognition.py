"""
===================================================
基于LFW数据集的人脸识别系统 - SVM实现
===================================================

本程序实现了基于Labeled Faces in the Wild (LFW)数据集的人脸识别系统
使用主成分分析(PCA)进行特征提取，支持向量机(SVM)进行分类

数据集来源: http://vis-www.cs.umass.edu/lfw/lfw-funneled.tgz (233MB)
LFW官网: http://vis-www.cs.umass.edu/lfw/

主要功能:
1. 数据加载和预处理
2. PCA特征提取(特征脸)
3. SVM分类器训练和优化
4. 模型性能评估
5. 结果可视化

作者: 学生姓名
日期: 2024年12月
课程: 非结构化数据挖掘
"""
from __future__ import print_function

# 导入必要的库
from time import time
import logging
import matplotlib.pyplot as plt
import numpy as np
import os
import seaborn as sns
from matplotlib import rcParams

# 导入sklearn相关模块
from sklearn.model_selection import train_test_split
from sklearn.model_selection import GridSearchCV
from sklearn.datasets import fetch_lfw_people
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_score, recall_score, f1_score
from sklearn.decomposition import PCA
from sklearn.svm import SVC

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

print(__doc__)

# 配置日志输出格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(message)s')

###############################################################################
# 第一步: 数据加载和基本信息统计
###############################################################################

print("=" * 60)
print("第一步: 加载LFW数据集")
print("=" * 60)

# 加载LFW数据集
# min_faces_per_person: 每个人最少的图片数量，用于过滤样本数过少的人物
# resize: 图像缩放比例，减小图像尺寸以提高计算效率

# 使用本地数据集
local_data_home = "./data"

try:
    # 首先尝试从本地加载
    if os.path.exists(local_data_home):
        print(f"📁 使用本地数据集: {local_data_home}")
        lfw_people = fetch_lfw_people(
            min_faces_per_person=70,  # 降低阈值以获得更多样本
            resize=0.4,
            data_home=local_data_home
        )
        print("✅ 本地数据集加载成功！")
    else:
        # 如果本地没有数据，使用自动下载
        print("⚠️  本地数据集不存在，尝试自动下载...")
        lfw_people = fetch_lfw_people(min_faces_per_person=70, resize=0.4)
        print("✅ 数据集自动下载成功！")

except Exception as e:
    print(f"❌ 数据集加载失败: {e}")
    print("请检查数据集是否正确放置在 './data' 文件夹中")
    raise

# 获取图像数组的维度信息(用于后续绘图)
n_samples, h, w = lfw_people.images.shape

# 获取特征数据(将图像展平为一维向量)
# 机器学习算法使用展平后的像素数据，忽略像素的相对位置信息
X = lfw_people.data
n_features = X.shape[1]

# 获取标签数据(人物ID)
y = lfw_people.target
target_names = lfw_people.target_names  # 人物姓名
n_classes = target_names.shape[0]       # 类别数量

# 打印数据集基本信息
print("数据集基本信息:")
print(f"样本总数: {n_samples}")
print(f"特征维度: {n_features}")
print(f"类别数量: {n_classes}")
print(f"图像尺寸: {h} x {w}")
print(f"标签形状: {y.shape}")
print("\n包含的人物:")
for i, name in enumerate(target_names):
    count = np.sum(y == i)
    print(f"{i+1:2d}. {name}: {count} 张图片")

# 可视化数据集样本分布
def plot_dataset_samples():
    """绘制数据集样本展示图"""
    plt.figure(figsize=(12, 8))

    # 绘制每个人的样本数量分布
    plt.subplot(2, 2, 1)
    counts = [np.sum(y == i) for i in range(n_classes)]
    plt.bar(range(n_classes), counts, color='skyblue', alpha=0.7)
    plt.title('各人物样本数量分布', fontsize=14, fontweight='bold')
    plt.xlabel('人物编号')
    plt.ylabel('样本数量')
    plt.xticks(range(n_classes), [name.split()[-1] for name in target_names], rotation=45)

    # 显示一些样本图像
    plt.subplot(2, 2, (2, 4))
    n_show = min(12, n_samples)
    indices = np.random.choice(n_samples, n_show, replace=False)

    for i, idx in enumerate(indices):
        plt.subplot(3, 4, i + 1)
        plt.imshow(lfw_people.images[idx], cmap='gray')
        plt.title(f'{target_names[y[idx]].split()[-1]}', fontsize=10)
        plt.xticks([])
        plt.yticks([])

    plt.tight_layout()
    plt.savefig('dataset_overview.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("📊 数据集概览图已保存为 'dataset_overview.png'")

# 调用可视化函数
plot_dataset_samples()


###############################################################################
# 第二步: 数据集划分
###############################################################################

print("\n" + "=" * 60)
print("第二步: 划分训练集和测试集")
print("=" * 60)

# 使用分层抽样将数据集划分为训练集和测试集
# test_size=0.25: 25%的数据用作测试集
# random_state=42: 设置随机种子，确保结果可重现
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.25, random_state=42)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")
print(f"训练标签形状: {y_train.shape}")
print(f"测试标签形状: {y_test.shape}")

###############################################################################
# 第三步: PCA特征提取(特征脸)
###############################################################################

print("\n" + "=" * 60)
print("第三步: PCA特征提取")
print("=" * 60)

# 设置主成分数量
n_components = 150

print(f"从 {X_train.shape[0]} 张人脸图像中提取前 {n_components} 个特征脸...")
t0 = time()

# 创建PCA对象并训练
# n_components: 保留的主成分数量
# svd_solver='randomized': 使用随机化SVD算法，适合大数据集
# whiten=True: 白化处理，使特征具有单位方差
pca = PCA(n_components=n_components, svd_solver='randomized', whiten=True)
pca.fit(X_train)

print(f"PCA训练完成，耗时: {time() - t0:.3f}秒")

# 分析PCA结果
eigenfaces = pca.components_.reshape((n_components, h, w))
explained_variance_ratio = pca.explained_variance_ratio_
cumulative_variance_ratio = np.cumsum(explained_variance_ratio)

print(f"前{n_components}个主成分解释的方差比例: {cumulative_variance_ratio[-1]:.4f}")
print(f"前10个主成分解释的方差比例: {cumulative_variance_ratio[9]:.4f}")
print(f"前50个主成分解释的方差比例: {cumulative_variance_ratio[49]:.4f}")

# 可视化PCA分析结果
def plot_pca_analysis():
    """绘制PCA分析结果"""
    plt.figure(figsize=(15, 10))

    # 1. 方差解释比例图
    plt.subplot(2, 3, 1)
    plt.plot(range(1, min(51, n_components+1)), explained_variance_ratio[:50], 'b-', linewidth=2)
    plt.title('前50个主成分的方差解释比例', fontsize=12, fontweight='bold')
    plt.xlabel('主成分编号')
    plt.ylabel('方差解释比例')
    plt.grid(True, alpha=0.3)

    # 2. 累积方差解释比例图
    plt.subplot(2, 3, 2)
    plt.plot(range(1, min(101, n_components+1)), cumulative_variance_ratio[:100], 'r-', linewidth=2)
    plt.title('累积方差解释比例', fontsize=12, fontweight='bold')
    plt.xlabel('主成分数量')
    plt.ylabel('累积方差解释比例')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0.95, color='g', linestyle='--', alpha=0.7, label='95%阈值')
    plt.legend()

    # 3-6. 显示前4个特征脸
    for i in range(4):
        plt.subplot(2, 3, i + 3)
        plt.imshow(eigenfaces[i], cmap='gray')
        plt.title(f'特征脸 {i+1}\n(方差解释比例: {explained_variance_ratio[i]:.4f})', fontsize=10)
        plt.xticks([])
        plt.yticks([])

    plt.tight_layout()
    plt.savefig('pca_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("📊 PCA分析图已保存为 'pca_analysis.png'")

# 调用PCA分析可视化
plot_pca_analysis()

print("将输入数据投影到特征脸正交基上...")
t0 = time()
X_train_pca = pca.transform(X_train)
X_test_pca = pca.transform(X_test)
print(f"数据投影完成，耗时: {time() - t0:.3f}秒")

print(f"PCA后训练集形状: {X_train_pca.shape}")
print(f"PCA后测试集形状: {X_test_pca.shape}")

###############################################################################
# 第四步: SVM分类器训练和超参数优化
###############################################################################

print("\n" + "=" * 60)
print("第四步: SVM分类器训练")
print("=" * 60)

print("使用网格搜索优化SVM超参数...")
t0 = time()

# 定义超参数搜索空间
# C: 正则化参数，控制模型复杂度
# gamma: RBF核函数参数，控制单个训练样本的影响范围
param_grid = {
    'C': [1e3, 5e3, 1e4, 5e4, 1e5],
    'gamma': [0.0001, 0.0005, 0.001, 0.005, 0.01, 0.1]
}

print(f"搜索空间: C={param_grid['C']}")
print(f"         gamma={param_grid['gamma']}")
print(f"总共需要测试: {len(param_grid['C']) * len(param_grid['gamma'])} 种参数组合")

# 创建SVM分类器并进行网格搜索
# kernel='rbf': 使用径向基函数核
# class_weight='balanced': 自动调整类别权重以处理不平衡数据
clf = GridSearchCV(
    SVC(kernel='rbf', class_weight='balanced'),
    param_grid,
    cv=3,  # 3折交叉验证
    scoring='accuracy',
    n_jobs=-1,  # 使用所有CPU核心
    verbose=1
)

clf = clf.fit(X_train_pca, y_train)
training_time = time() - t0

print(f"SVM训练完成，耗时: {training_time:.3f}秒")
print(f"最佳参数组合: {clf.best_params_}")
print(f"最佳交叉验证得分: {clf.best_score_:.4f}")
print(f"最佳估计器: {clf.best_estimator_}")


###############################################################################
# 第五步: 模型性能评估
###############################################################################

print("\n" + "=" * 60)
print("第五步: 模型性能评估")
print("=" * 60)

print("在测试集上进行预测...")
t0 = time()
y_pred = clf.predict(X_test_pca)
prediction_time = time() - t0

print(f"预测完成，耗时: {prediction_time:.3f}秒")
print(f"平均每张图片预测时间: {prediction_time/len(y_test)*1000:.2f}毫秒")

# 计算各种性能指标
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, average='weighted')
recall = recall_score(y_test, y_pred, average='weighted')
f1 = f1_score(y_test, y_pred, average='weighted')

print("\n" + "=" * 40)
print("模型性能指标:")
print("=" * 40)
print(f"准确率 (Accuracy):  {accuracy:.4f}")
print(f"精确率 (Precision): {precision:.4f}")
print(f"召回率 (Recall):    {recall:.4f}")
print(f"F1分数 (F1-Score):  {f1:.4f}")

print("\n详细分类报告:")
print(classification_report(y_test, y_pred, target_names=target_names))

# 计算混淆矩阵
cm = confusion_matrix(y_test, y_pred, labels=range(n_classes))
print("\n混淆矩阵:")
print(cm)

# 可视化模型性能
def plot_performance_metrics():
    """绘制模型性能指标图"""
    plt.figure(figsize=(15, 10))

    # 1. 性能指标柱状图
    plt.subplot(2, 3, 1)
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
    values = [accuracy, precision, recall, f1]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    bars = plt.bar(metrics, values, color=colors, alpha=0.8)
    plt.title('模型性能指标', fontsize=14, fontweight='bold')
    plt.ylabel('分数')
    plt.ylim(0, 1)

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

    # 2. 混淆矩阵热力图
    plt.subplot(2, 3, (2, 3))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=[name.split()[-1] for name in target_names],
                yticklabels=[name.split()[-1] for name in target_names])
    plt.title('混淆矩阵', fontsize=14, fontweight='bold')
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')

    # 3. 每个类别的精确率
    plt.subplot(2, 3, 4)
    class_precision = precision_score(y_test, y_pred, average=None)
    plt.bar(range(n_classes), class_precision, color='lightcoral', alpha=0.7)
    plt.title('各类别精确率', fontsize=12, fontweight='bold')
    plt.xlabel('人物编号')
    plt.ylabel('精确率')
    plt.xticks(range(n_classes), [name.split()[-1] for name in target_names], rotation=45)

    # 4. 每个类别的召回率
    plt.subplot(2, 3, 5)
    class_recall = recall_score(y_test, y_pred, average=None)
    plt.bar(range(n_classes), class_recall, color='lightblue', alpha=0.7)
    plt.title('各类别召回率', fontsize=12, fontweight='bold')
    plt.xlabel('人物编号')
    plt.ylabel('召回率')
    plt.xticks(range(n_classes), [name.split()[-1] for name in target_names], rotation=45)

    # 5. 每个类别的F1分数
    plt.subplot(2, 3, 6)
    class_f1 = f1_score(y_test, y_pred, average=None)
    plt.bar(range(n_classes), class_f1, color='lightgreen', alpha=0.7)
    plt.title('各类别F1分数', fontsize=12, fontweight='bold')
    plt.xlabel('人物编号')
    plt.ylabel('F1分数')
    plt.xticks(range(n_classes), [name.split()[-1] for name in target_names], rotation=45)

    plt.tight_layout()
    plt.savefig('performance_metrics.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("📊 性能指标图已保存为 'performance_metrics.png'")

# 调用性能可视化函数
plot_performance_metrics()


###############################################################################
# 第六步: 预测结果可视化
###############################################################################

print("\n" + "=" * 60)
print("第六步: 预测结果可视化")
print("=" * 60)

def plot_gallery(images, titles, h, w, n_row=3, n_col=4, figsize=(12, 9), save_name=None):
    """绘制图像画廊的辅助函数"""
    plt.figure(figsize=figsize)
    plt.subplots_adjust(bottom=0, left=.01, right=.99, top=.90, hspace=.35)
    for i in range(min(n_row * n_col, len(images))):
        plt.subplot(n_row, n_col, i + 1)
        plt.imshow(images[i].reshape((h, w)), cmap=plt.cm.gray)
        plt.title(titles[i], size=10)
        plt.xticks(())
        plt.yticks(())

    if save_name:
        plt.savefig(save_name, dpi=300, bbox_inches='tight')
        print(f"📊 图像已保存为 '{save_name}'")
    plt.show()

def create_prediction_title(y_pred, y_test, target_names, i):
    """创建预测结果标题"""
    pred_name = target_names[y_pred[i]].rsplit(' ', 1)[-1]
    true_name = target_names[y_test[i]].rsplit(' ', 1)[-1]
    is_correct = "✓" if y_pred[i] == y_test[i] else "✗"
    return f'{is_correct} 预测: {pred_name}\n真实: {true_name}'

# 绘制测试集预测结果
print("绘制测试集预测结果...")
prediction_titles = [create_prediction_title(y_pred, y_test, target_names, i)
                     for i in range(min(12, y_pred.shape[0]))]

plot_gallery(X_test[:12], prediction_titles, h, w, n_row=3, n_col=4,
             save_name='prediction_results.png')

# 绘制特征脸
print("绘制特征脸...")
eigenface_titles = [f"特征脸 {i+1}" for i in range(min(12, eigenfaces.shape[0]))]
plot_gallery(eigenfaces[:12], eigenface_titles, h, w, n_row=3, n_col=4,
             save_name='eigenfaces.png')

# 绘制正确和错误预测的对比
print("绘制预测正确和错误的样本对比...")
correct_indices = np.where(y_pred == y_test)[0]
incorrect_indices = np.where(y_pred != y_test)[0]

if len(correct_indices) > 0 and len(incorrect_indices) > 0:
    plt.figure(figsize=(15, 8))

    # 正确预测的样本
    plt.subplot(2, 1, 1)
    n_correct_show = min(6, len(correct_indices))
    for i in range(n_correct_show):
        idx = correct_indices[i]
        plt.subplot(2, 6, i + 1)
        plt.imshow(X_test[idx].reshape((h, w)), cmap='gray')
        pred_name = target_names[y_pred[idx]].split()[-1]
        plt.title(f'✓ {pred_name}', color='green', fontsize=10)
        plt.xticks([])
        plt.yticks([])

    # 错误预测的样本
    n_incorrect_show = min(6, len(incorrect_indices))
    for i in range(n_incorrect_show):
        idx = incorrect_indices[i]
        plt.subplot(2, 6, i + 7)
        plt.imshow(X_test[idx].reshape((h, w)), cmap='gray')
        pred_name = target_names[y_pred[idx]].split()[-1]
        true_name = target_names[y_test[idx]].split()[-1]
        plt.title(f'✗ 预测:{pred_name}\n真实:{true_name}', color='red', fontsize=9)
        plt.xticks([])
        plt.yticks([])

    plt.suptitle('预测结果对比 (上排:正确预测, 下排:错误预测)', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('prediction_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("📊 预测对比图已保存为 'prediction_comparison.png'")

print("\n" + "=" * 60)
print("人脸识别系统分析完成！")
print("=" * 60)
print(f"📈 最终准确率: {accuracy:.4f}")
print(f"⏱️  训练时间: {training_time:.2f}秒")
print(f"⚡ 预测时间: {prediction_time:.3f}秒")
print("📁 生成的图表文件:")
print("   - dataset_overview.png (数据集概览)")
print("   - pca_analysis.png (PCA分析)")
print("   - performance_metrics.png (性能指标)")
print("   - prediction_results.png (预测结果)")
print("   - eigenfaces.png (特征脸)")
print("   - prediction_comparison.png (预测对比)")
print("=" * 60)
